"""Main FastAPI application for Gemfinder."""

import logging
from datetime import datetime, timedelta, timezone
from typing import List, Optional
from contextlib import asynccontextmanager

from fastapi import FastAPI, Depends, HTTPException, Request, Response
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
from sqlalchemy.orm import Session
from sqlalchemy import desc, func, and_

from app.database import get_db, init_db, test_connection
from app.models import Swap, SwapResponse, TokenStats, Token, VolumeAnomaly, LargeSwap
from app.transaction_decoder import transaction_log_listener
from app.volume_analyzer import VolumeAnalyzer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events."""
    # Startup
    logger.info("Starting Gemfinder application...")

    # Test database connection
    if not test_connection():
        logger.error("Failed to connect to database")
        raise Exception("Database connection failed")

    # Initialize database
    try:
        init_db()
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise

    # Start Solana WebSocket listener
    try:
        transaction_log_listener.start()
        logger.info("Started Solana WebSocket listener")
    except Exception as e:
        logger.error(f"Failed to start WebSocket listener: {e}")
        # Don't raise - app can still work without live data

    yield

    # Shutdown
    logger.info("Shutting down Gemfinder application...")

    # Stop WebSocket listener
    try:
        transaction_log_listener.stop()
        logger.info("Stopped Solana WebSocket listener")
    except Exception as e:
        logger.error(f"Error stopping WebSocket listener: {e}")


# Create FastAPI app
app = FastAPI(
    title="Gemfinder",
    description="Solana pump.fun swap tracker",
    version="0.1.0",
    lifespan=lifespan
)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Templates
templates = Jinja2Templates(directory="app/templates")


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": datetime.now(timezone.utc),
        "database": test_connection(),
        "websocket_listener": transaction_log_listener.running
    }


@app.get("/", response_class=HTMLResponse)
async def dashboard(request: Request, db: Session = Depends(get_db)):
    """Main dashboard page with volume anomaly detection."""
    try:
        # Initialize volume analyzer
        analyzer = VolumeAnalyzer()

        # Get volume anomalies (top 10)
        volume_anomalies = analyzer.get_volume_anomalies(limit=10)

        # Get large swaps for each anomaly token
        anomaly_data = []
        for anomaly in volume_anomalies:
            large_swaps = analyzer.get_large_swaps_for_token(anomaly.mint_address, limit=3)
            anomaly_data.append({
                'anomaly': anomaly,
                'large_swaps': large_swaps
            })

        # Get total counts
        total_swaps = db.query(func.count(Swap.id)).scalar()
        unique_tokens = db.query(func.count(func.distinct(Swap.mint_address))).scalar()

        # Get swaps from last 24 hours
        yesterday = datetime.now(timezone.utc) - timedelta(hours=24)
        recent_count = db.query(func.count(Swap.id)).filter(Swap.timestamp >= yesterday).scalar()

        # Get large swaps count from last hour
        hour_ago = datetime.now(timezone.utc) - timedelta(hours=1)
        large_swaps_count = db.query(func.count(Swap.id)).filter(
            and_(
                Swap.timestamp >= hour_ago,
                Swap.sol_amount >= analyzer.LARGE_TRADE_THRESHOLD
            )
        ).scalar()

        return templates.TemplateResponse("index.html", {
            "request": request,
            "anomaly_data": anomaly_data,
            "total_swaps": total_swaps or 0,
            "unique_tokens": unique_tokens or 0,
            "recent_count": recent_count or 0,
            "large_swaps_count": large_swaps_count or 0
        })

    except Exception as e:
        logger.error(f"Error loading dashboard: {e}")
        raise HTTPException(status_code=500, detail="Failed to load dashboard")


@app.get("/api/swaps", response_model=List[SwapResponse])
async def get_swaps(
    limit: int = 50,
    offset: int = 0,
    mint_address: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Get swaps with optional filtering."""
    try:
        query = db.query(Swap)

        if mint_address:
            query = query.filter(Swap.mint_address == mint_address)

        swaps = query.order_by(desc(Swap.timestamp)).offset(offset).limit(limit).all()

        # Add seconds_ago calculation
        now = datetime.now(timezone.utc)
        swap_responses = []
        for swap in swaps:
            swap_dict = swap.__dict__.copy()
            swap_dict['seconds_ago'] = (now - swap.timestamp).total_seconds()
            swap_responses.append(SwapResponse(**swap_dict))

        return swap_responses

    except Exception as e:
        logger.error(f"Error fetching swaps: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch swaps")


@app.get("/api/anomalies/recent")
async def get_volume_anomalies_htmx(
    limit: int = 10,
    db: Session = Depends(get_db)
):
    """Get volume anomalies for HTMX updates."""
    try:
        # Initialize volume analyzer
        analyzer = VolumeAnalyzer()

        # Get volume anomalies
        volume_anomalies = analyzer.get_volume_anomalies(limit=limit)

        # Return HTML fragment for HTMX
        html_content = ""

        if not volume_anomalies:
            html_content = """
            <div class="px-6 py-8 text-center text-gray-400">
                <p class="text-lg">No volume anomalies detected</p>
                <p class="text-sm mt-2">Waiting for significant trading activity...</p>
            </div>
            """
        else:
            for anomaly in volume_anomalies:
                # Get recent large swaps for this token
                large_swaps = analyzer.get_large_swaps_for_token(anomaly.mint_address, limit=3)

                # Token display name
                token_name = anomaly.token_symbol or anomaly.token_name or f"{anomaly.mint_address[:8]}...{anomaly.mint_address[-8:]}"

                # Anomaly score color coding
                if anomaly.overall_anomaly_score >= 10:
                    score_color = "text-red-400"
                    score_icon = "🔥"
                elif anomaly.overall_anomaly_score >= 5:
                    score_color = "text-orange-400"
                    score_icon = "⚡"
                else:
                    score_color = "text-yellow-400"
                    score_icon = "📈"

                # Market cap display
                market_cap_display = f"${anomaly.market_cap_estimate:,.0f}" if anomaly.market_cap_estimate else "N/A"

                html_content += f"""
                <div class="bg-gray-800 rounded-lg p-4 mb-4 border border-gray-700">
                    <div class="flex justify-between items-center mb-3">
                        <div class="flex items-center space-x-3">
                            <a href="https://dexscreener.com/solana/{anomaly.mint_address}" target="_blank"
                               class="text-lg font-bold text-purple-400 hover:text-purple-300 transition-colors">
                                {token_name}
                                <span class="text-xs text-gray-400 ml-1">↗</span>
                            </a>
                            <span class="{score_color} font-bold">{score_icon}</span>
                        </div>
                        <div class="text-sm text-gray-400">
                            {anomaly.swap_count_1h} swaps (30m)
                        </div>
                    </div>

                    <div class="grid grid-cols-4 gap-4 mb-3 text-sm">
                        <div>
                            <span class="text-gray-400">30m Volume:</span>
                            <span class="text-yellow-400 font-bold">{anomaly.volume_1h:.3f} SOL</span>
                        </div>
                        <div>
                            <span class="text-gray-400">15m Volume:</span>
                            <span class="text-yellow-400">{anomaly.volume_6h:.3f} SOL</span>
                        </div>
                        <div>
                            <span class="text-gray-400">5m Volume:</span>
                            <span class="text-yellow-400">{anomaly.volume_24h:.3f} SOL</span>
                        </div>
                        <div>
                            <span class="text-gray-400">Market Cap:</span>
                            <span class="text-green-400">{market_cap_display}</span>
                        </div>
                    </div>
                """

                # Add large swaps for this token
                if large_swaps:
                    html_content += """
                    <div class="border-t border-gray-700 pt-3">
                        <h4 class="text-sm font-semibold text-gray-300 mb-2">Recent Large Trades:</h4>
                        <div class="space-y-1">
                    """

                    for swap in large_swaps:
                        time_str = f"{int(swap.seconds_ago)}s ago" if swap.seconds_ago < 60 else f"{int(swap.seconds_ago/60)}m ago"
                        type_color = "text-green-400" if swap.swap_type == "buy" else "text-red-400"
                        type_icon = "🟢" if swap.swap_type == "buy" else "🔴"
                        whale_indicator = "🐋" if swap.is_whale_trade else ""

                        html_content += f"""
                        <div class="flex justify-between items-center text-xs bg-gray-900 rounded p-2">
                            <div class="flex items-center space-x-2">
                                <span class="{type_color}">{type_icon} {swap.swap_type.title()}</span>
                                <span class="text-yellow-400 font-bold">{swap.sol_display:.3f} SOL</span>
                                <span class="text-blue-400">{swap.token_display:.2f}</span>
                                {whale_indicator}
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="text-gray-400">{time_str}</span>
                                <a href="https://solscan.io/tx/{swap.signature}" target="_blank"
                                   class="text-blue-400 hover:text-blue-300">→</a>
                            </div>
                        </div>
                        """

                    html_content += """
                        </div>
                    </div>
                    """

                html_content += "</div>"

        return Response(content=html_content, media_type="text/html")

    except Exception as e:
        logger.error(f"Error fetching volume anomalies: {e}")
        return Response(content="<div class='px-4 py-8 text-center text-gray-400'>Error loading anomalies</div>", media_type="text/html")


@app.get("/api/stats")
async def get_stats(db: Session = Depends(get_db)):
    """Get application statistics."""
    try:
        # Initialize volume analyzer for thresholds
        analyzer = VolumeAnalyzer()

        total_swaps = db.query(func.count(Swap.id)).scalar()
        unique_tokens = db.query(func.count(func.distinct(Swap.mint_address))).scalar()

        # Get swaps from different time periods
        now = datetime.now(timezone.utc)
        hour_ago = now - timedelta(hours=1)
        day_ago = now - timedelta(days=1)

        swaps_last_day = db.query(func.count(Swap.id)).filter(Swap.timestamp >= day_ago).scalar()

        # Get large swaps count from last hour
        large_swaps_last_hour = db.query(func.count(Swap.id)).filter(
            and_(
                Swap.timestamp >= hour_ago,
                Swap.sol_amount >= analyzer.LARGE_TRADE_THRESHOLD
            )
        ).scalar()

        return {
            "total_swaps": total_swaps or 0,
            "unique_tokens": unique_tokens or 0,
            "swaps_last_hour": large_swaps_last_hour or 0,  # Return large swaps for the UI
            "swaps_last_day": swaps_last_day or 0,
            "timestamp": now
        }

    except Exception as e:
        logger.error(f"Error fetching stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch statistics")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
