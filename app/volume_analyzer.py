"""Volume anomaly detection and analysis service."""

import logging
from datetime import datetime, timezone, timedelta
from typing import List
from sqlalchemy.orm import Session
from sqlalchemy import text
from app.models import VolumeAnomaly, LargeSwap
from app.database import SessionLocal

logger = logging.getLogger(__name__)

class VolumeAnalyzer:
    """Service for detecting volume anomalies and analyzing trading patterns."""

    # Thresholds for significant trades (in lamports)
    LARGE_TRADE_THRESHOLD = 1_000_000_000  # 1 SOL
    WHALE_TRADE_THRESHOLD = 10_000_000_000  # 10 SOL

    # Minimum volume thresholds for anomaly detection (in SOL)
    MIN_VOLUME_1H = 0.1  # Minimum 0.1 SOL volume in 1h to be considered
    MIN_VOLUME_6H = 0.5  # Minimum 0.5 SOL volume in 6h to be considered
    MIN_VOLUME_24H = 1.0  # Minimum 1.0 SOL volume in 24h to be considered

    # Anomaly score thresholds
    MIN_ANOMALY_SCORE = 2.0  # Minimum 2x increase to be considered anomalous
    HIGH_ANOMALY_SCORE = 5.0  # 5x increase is highly anomalous

    def __init__(self):
        self.db = SessionLocal()

    def __del__(self):
        if hasattr(self, 'db'):
            self.db.close()

    def get_volume_anomalies(self, limit: int = 10) -> List[VolumeAnomaly]:
        """Get tokens with volume anomalies using efficient SQL queries."""
        try:
            now = datetime.now(timezone.utc)

            # Time windows
            hour_ago = now - timedelta(hours=1)
            six_hours_ago = now - timedelta(hours=6)
            day_ago = now - timedelta(hours=24)
            week_ago = now - timedelta(days=7)

            # Convert thresholds to lamports for SQL
            min_volume_1h_lamports = int(self.MIN_VOLUME_1H * 1_000_000_000)
            min_volume_6h_lamports = int(self.MIN_VOLUME_6H * 1_000_000_000)
            min_volume_24h_lamports = int(self.MIN_VOLUME_24H * 1_000_000_000)

            # Single optimized query to get volume anomalies
            query = text("""
                WITH recent_volumes AS (
                    SELECT
                        s.mint_address,
                        t.name as token_name,
                        t.symbol as token_symbol,
                        -- Current period volumes
                        COALESCE(SUM(CASE WHEN s.timestamp >= :hour_ago THEN s.sol_amount ELSE 0 END), 0) as volume_1h,
                        COALESCE(SUM(CASE WHEN s.timestamp >= :six_hours_ago THEN s.sol_amount ELSE 0 END), 0) as volume_6h,
                        COALESCE(SUM(CASE WHEN s.timestamp >= :day_ago THEN s.sol_amount ELSE 0 END), 0) as volume_24h,
                        -- Swap counts
                        COUNT(CASE WHEN s.timestamp >= :hour_ago THEN 1 END) as swap_count_1h,
                        COUNT(CASE WHEN s.timestamp >= :six_hours_ago THEN 1 END) as swap_count_6h,
                        COUNT(CASE WHEN s.timestamp >= :day_ago THEN 1 END) as swap_count_24h,
                        -- Largest swap in 1h
                        COALESCE(MAX(CASE WHEN s.timestamp >= :hour_ago THEN s.sol_amount END), 0) as largest_swap_1h,
                        -- Last activity
                        MAX(s.timestamp) as last_activity
                    FROM swaps s
                    JOIN tokens t ON s.mint_address = t.mint_address
                    WHERE s.timestamp >= :day_ago
                    GROUP BY s.mint_address, t.name, t.symbol
                ),
                historical_volumes AS (
                    SELECT
                        s.mint_address,
                        -- Historical averages (excluding current day)
                        COALESCE(AVG(s.sol_amount), 0) * 24 as avg_volume_1h_raw,  -- Approximate hourly average
                        COALESCE(AVG(s.sol_amount), 0) * 4 as avg_volume_6h_raw,   -- Approximate 6h average
                        COALESCE(SUM(s.sol_amount), 0) / GREATEST(EXTRACT(EPOCH FROM (:day_ago - :week_ago)) / 86400, 1) as avg_volume_24h_raw
                    FROM swaps s
                    WHERE s.timestamp >= :week_ago AND s.timestamp < :day_ago
                    GROUP BY s.mint_address
                )
                SELECT
                    rv.mint_address,
                    rv.token_name,
                    rv.token_symbol,
                    rv.volume_1h,
                    rv.volume_6h,
                    rv.volume_24h,
                    COALESCE(hv.avg_volume_1h_raw, 1000000000) as avg_volume_1h,  -- Default 1 SOL if no history
                    COALESCE(hv.avg_volume_6h_raw, 1000000000) as avg_volume_6h,
                    COALESCE(hv.avg_volume_24h_raw, 1000000000) as avg_volume_24h,
                    rv.swap_count_1h,
                    rv.swap_count_6h,
                    rv.swap_count_24h,
                    rv.largest_swap_1h,
                    rv.last_activity,
                    -- Calculate anomaly scores
                    CASE WHEN COALESCE(hv.avg_volume_1h_raw, 1000000000) > 0
                         THEN rv.volume_1h::float / COALESCE(hv.avg_volume_1h_raw, 1000000000)
                         ELSE 0 END as anomaly_score_1h,
                    CASE WHEN COALESCE(hv.avg_volume_6h_raw, 1000000000) > 0
                         THEN rv.volume_6h::float / COALESCE(hv.avg_volume_6h_raw, 1000000000)
                         ELSE 0 END as anomaly_score_6h,
                    CASE WHEN COALESCE(hv.avg_volume_24h_raw, 1000000000) > 0
                         THEN rv.volume_24h::float / COALESCE(hv.avg_volume_24h_raw, 1000000000)
                         ELSE 0 END as anomaly_score_24h
                FROM recent_volumes rv
                LEFT JOIN historical_volumes hv ON rv.mint_address = hv.mint_address
                WHERE (rv.volume_1h >= :min_volume_1h OR rv.volume_6h >= :min_volume_6h OR rv.volume_24h >= :min_volume_24h)
                ORDER BY (
                    CASE WHEN COALESCE(hv.avg_volume_1h_raw, 1000000000) > 0
                         THEN rv.volume_1h::float / COALESCE(hv.avg_volume_1h_raw, 1000000000)
                         ELSE 0 END * 0.5 +
                    CASE WHEN COALESCE(hv.avg_volume_6h_raw, 1000000000) > 0
                         THEN rv.volume_6h::float / COALESCE(hv.avg_volume_6h_raw, 1000000000)
                         ELSE 0 END * 0.3 +
                    CASE WHEN COALESCE(hv.avg_volume_24h_raw, 1000000000) > 0
                         THEN rv.volume_24h::float / COALESCE(hv.avg_volume_24h_raw, 1000000000)
                         ELSE 0 END * 0.2
                ) DESC
                LIMIT :limit
            """)

            result = self.db.execute(query, {
                'hour_ago': hour_ago,
                'six_hours_ago': six_hours_ago,
                'day_ago': day_ago,
                'week_ago': week_ago,
                'min_volume_1h': min_volume_1h_lamports,
                'min_volume_6h': min_volume_6h_lamports,
                'min_volume_24h': min_volume_24h_lamports,
                'limit': limit * 2  # Get more results to filter by min anomaly score
            })

            anomalies = []
            for row in result:
                # Calculate overall anomaly score
                overall_score = (
                    row.anomaly_score_1h * 0.5 +
                    row.anomaly_score_6h * 0.3 +
                    row.anomaly_score_24h * 0.2
                )

                # Only include if meets minimum anomaly threshold
                if overall_score >= self.MIN_ANOMALY_SCORE:
                    anomalies.append(VolumeAnomaly(
                        mint_address=row.mint_address,
                        token_name=row.token_name,
                        token_symbol=row.token_symbol,
                        volume_1h=float(row.volume_1h) / 1_000_000_000,  # Convert to SOL
                        volume_6h=float(row.volume_6h) / 1_000_000_000,
                        volume_24h=float(row.volume_24h) / 1_000_000_000,
                        avg_volume_1h=float(row.avg_volume_1h) / 1_000_000_000,
                        avg_volume_6h=float(row.avg_volume_6h) / 1_000_000_000,
                        avg_volume_24h=float(row.avg_volume_24h) / 1_000_000_000,
                        anomaly_score_1h=row.anomaly_score_1h,
                        anomaly_score_6h=row.anomaly_score_6h,
                        anomaly_score_24h=row.anomaly_score_24h,
                        overall_anomaly_score=overall_score,
                        swap_count_1h=row.swap_count_1h,
                        swap_count_6h=row.swap_count_6h,
                        swap_count_24h=row.swap_count_24h,
                        largest_swap_1h=float(row.largest_swap_1h) / 1_000_000_000,
                        last_activity=row.last_activity
                    ))

            return anomalies[:limit]

        except Exception as e:
            logger.error(f"Error getting volume anomalies: {e}")
            return []

    def get_large_swaps_for_token(self, mint_address: str, limit: int = 5) -> List[LargeSwap]:
        """Get recent large swaps for a specific token using optimized query."""
        try:
            now = datetime.now(timezone.utc)
            hour_ago = now - timedelta(hours=1)

            # Optimized query for large swaps
            query = text("""
                SELECT
                    s.signature,
                    s.timestamp,
                    s.mint_address,
                    s.token_amount,
                    s.sol_amount,
                    s.price_per_token,
                    s.swap_type,
                    s.user_wallet,
                    t.name as token_name,
                    t.symbol as token_symbol,
                    t.decimals as token_decimals,
                    EXTRACT(EPOCH FROM (:now - s.timestamp)) as seconds_ago
                FROM swaps s
                JOIN tokens t ON s.mint_address = t.mint_address
                WHERE s.mint_address = :mint_address
                  AND s.sol_amount >= :large_trade_threshold
                  AND s.timestamp >= :hour_ago
                ORDER BY s.timestamp DESC
                LIMIT :limit
            """)

            result = self.db.execute(query, {
                'mint_address': mint_address,
                'large_trade_threshold': self.LARGE_TRADE_THRESHOLD,
                'hour_ago': hour_ago,
                'now': now,
                'limit': limit
            })

            large_swaps = []
            for row in result:
                sol_display = row.sol_amount / 1_000_000_000
                token_display = row.token_amount / (10 ** row.token_decimals)

                large_swaps.append(LargeSwap(
                    signature=row.signature,
                    mint_address=row.mint_address,
                    token_name=row.token_name,
                    token_symbol=row.token_symbol,
                    token_decimals=row.token_decimals,
                    timestamp=row.timestamp,
                    sol_amount=row.sol_amount,
                    token_amount=row.token_amount,
                    sol_display=sol_display,
                    token_display=token_display,
                    price_per_token=row.price_per_token,
                    swap_type=row.swap_type,
                    user_wallet=row.user_wallet,
                    is_whale_trade=row.sol_amount >= self.WHALE_TRADE_THRESHOLD,
                    is_large_trade=row.sol_amount >= self.LARGE_TRADE_THRESHOLD,
                    seconds_ago=row.seconds_ago
                ))

            return large_swaps

        except Exception as e:
            logger.error(f"Error getting large swaps for {mint_address}: {e}")
            return []