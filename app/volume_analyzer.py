"""Volume anomaly detection and analysis service."""

import logging
from datetime import datetime, timezone, timedelta
from typing import List
from sqlalchemy import text
from app.models import VolumeAnomaly, LargeSwap
from app.database import SessionLocal

logger = logging.getLogger(__name__)

class VolumeAnalyzer:
    """Service for detecting volume anomalies and analyzing trading patterns."""

    # Thresholds for significant trades (in lamports) - lowered for pumpfun tokens
    LARGE_TRADE_THRESHOLD = 500_000_000   # 0.5 SOL (lowered from 1 SOL)
    WHALE_TRADE_THRESHOLD = 5_000_000_000  # 5 SOL (lowered from 10 SOL)

    # Minimum volume thresholds for anomaly detection (in SOL) - optimized for short timeframes
    MIN_VOLUME_5M = 0.01   # Minimum 0.01 SOL volume in 5min to be considered
    MIN_VOLUME_15M = 0.05  # Minimum 0.05 SOL volume in 15min to be considered
    MIN_VOLUME_30M = 0.1   # Minimum 0.1 SOL volume in 30min to be considered

    # Anomaly score thresholds - more sensitive for rapid detection
    MIN_ANOMALY_SCORE = 1.5  # Minimum 1.5x increase (lowered from 2x)
    HIGH_ANOMALY_SCORE = 3.0  # 3x increase is highly anomalous (lowered from 5x)

    # Maximum age for transactions to consider (in minutes)
    MAX_TRANSACTION_AGE_MINUTES = 60  # Only process transactions from last hour

    def __init__(self):
        self.db = SessionLocal()

    def __del__(self):
        if hasattr(self, 'db'):
            self.db.close()

    def get_volume_anomalies(self, limit: int = 10) -> List[VolumeAnomaly]:
        """Get tokens with volume anomalies using efficient SQL queries optimized for pumpfun tokens."""
        try:
            now = datetime.now(timezone.utc)

            # Short time windows optimized for pumpfun tokens
            five_min_ago = now - timedelta(minutes=5)
            fifteen_min_ago = now - timedelta(minutes=15)
            thirty_min_ago = now - timedelta(minutes=30)
            max_age_ago = now - timedelta(minutes=self.MAX_TRANSACTION_AGE_MINUTES)

            # Convert thresholds to lamports for SQL
            min_volume_5m_lamports = int(self.MIN_VOLUME_5M * 1_000_000_000)
            min_volume_15m_lamports = int(self.MIN_VOLUME_15M * 1_000_000_000)
            min_volume_30m_lamports = int(self.MIN_VOLUME_30M * 1_000_000_000)

            # Single optimized query to get volume anomalies with short timeframes
            query = text("""
                WITH recent_volumes AS (
                    SELECT
                        s.mint_address,
                        t.name as token_name,
                        t.symbol as token_symbol,
                        -- Current period volumes (short timeframes)
                        COALESCE(SUM(CASE WHEN s.timestamp >= :five_min_ago THEN s.sol_amount ELSE 0 END), 0) as volume_5m,
                        COALESCE(SUM(CASE WHEN s.timestamp >= :fifteen_min_ago THEN s.sol_amount ELSE 0 END), 0) as volume_15m,
                        COALESCE(SUM(CASE WHEN s.timestamp >= :thirty_min_ago THEN s.sol_amount ELSE 0 END), 0) as volume_30m,
                        -- Swap counts
                        COUNT(CASE WHEN s.timestamp >= :five_min_ago THEN 1 END) as swap_count_5m,
                        COUNT(CASE WHEN s.timestamp >= :fifteen_min_ago THEN 1 END) as swap_count_15m,
                        COUNT(CASE WHEN s.timestamp >= :thirty_min_ago THEN 1 END) as swap_count_30m,
                        -- Largest swap in 5m (most recent activity)
                        COALESCE(MAX(CASE WHEN s.timestamp >= :five_min_ago THEN s.sol_amount END), 0) as largest_swap_5m,
                        -- Last activity
                        MAX(s.timestamp) as last_activity,
                        -- Volume velocity (SOL per minute in last 5 minutes)
                        COALESCE(SUM(CASE WHEN s.timestamp >= :five_min_ago THEN s.sol_amount ELSE 0 END), 0) / 5.0 as volume_velocity
                    FROM swaps s
                    JOIN tokens t ON s.mint_address = t.mint_address
                    WHERE s.timestamp >= :max_age_ago
                    GROUP BY s.mint_address, t.name, t.symbol
                ),
                historical_volumes AS (
                    SELECT
                        s.mint_address,
                        -- Historical averages for short periods (using last 2-4 hours as baseline, excluding current 30min)
                        COALESCE(AVG(s.sol_amount), 0) * 12 as avg_volume_5m_raw,   -- Approximate 5min average
                        COALESCE(AVG(s.sol_amount), 0) * 4 as avg_volume_15m_raw,   -- Approximate 15min average
                        COALESCE(AVG(s.sol_amount), 0) * 2 as avg_volume_30m_raw,   -- Approximate 30min average
                        COUNT(*) as historical_swap_count
                    FROM swaps s
                    WHERE s.timestamp >= :max_age_ago AND s.timestamp < :thirty_min_ago
                    GROUP BY s.mint_address
                )
                SELECT
                    rv.mint_address,
                    rv.token_name,
                    rv.token_symbol,
                    rv.volume_5m,
                    rv.volume_15m,
                    rv.volume_30m,
                    COALESCE(hv.avg_volume_5m_raw, 10000000) as avg_volume_5m,   -- Default 0.01 SOL if no history
                    COALESCE(hv.avg_volume_15m_raw, 50000000) as avg_volume_15m, -- Default 0.05 SOL if no history
                    COALESCE(hv.avg_volume_30m_raw, 100000000) as avg_volume_30m, -- Default 0.1 SOL if no history
                    rv.swap_count_5m,
                    rv.swap_count_15m,
                    rv.swap_count_30m,
                    rv.largest_swap_5m,
                    rv.last_activity,
                    rv.volume_velocity,
                    -- Calculate anomaly scores with higher sensitivity
                    CASE WHEN COALESCE(hv.avg_volume_5m_raw, 10000000) > 0
                         THEN rv.volume_5m::float / COALESCE(hv.avg_volume_5m_raw, 10000000)
                         ELSE 0 END as anomaly_score_5m,
                    CASE WHEN COALESCE(hv.avg_volume_15m_raw, 50000000) > 0
                         THEN rv.volume_15m::float / COALESCE(hv.avg_volume_15m_raw, 50000000)
                         ELSE 0 END as anomaly_score_15m,
                    CASE WHEN COALESCE(hv.avg_volume_30m_raw, 100000000) > 0
                         THEN rv.volume_30m::float / COALESCE(hv.avg_volume_30m_raw, 100000000)
                         ELSE 0 END as anomaly_score_30m,
                    -- Time decay factor (more recent = higher score)
                    CASE WHEN rv.last_activity IS NOT NULL
                         THEN 1.0 + (1.0 - EXTRACT(EPOCH FROM (:now - rv.last_activity)) / 1800.0)  -- 30min decay
                         ELSE 1.0 END as recency_factor
                FROM recent_volumes rv
                LEFT JOIN historical_volumes hv ON rv.mint_address = hv.mint_address
                WHERE (rv.volume_5m >= :min_volume_5m OR rv.volume_15m >= :min_volume_15m OR rv.volume_30m >= :min_volume_30m)
                  AND rv.last_activity >= :five_min_ago  -- Only tokens with very recent activity
                ORDER BY (
                    -- Weighted anomaly score prioritizing most recent activity
                    CASE WHEN COALESCE(hv.avg_volume_5m_raw, 10000000) > 0
                         THEN rv.volume_5m::float / COALESCE(hv.avg_volume_5m_raw, 10000000)
                         ELSE 0 END * 0.6 +  -- 60% weight on 5min activity
                    CASE WHEN COALESCE(hv.avg_volume_15m_raw, 50000000) > 0
                         THEN rv.volume_15m::float / COALESCE(hv.avg_volume_15m_raw, 50000000)
                         ELSE 0 END * 0.3 +  -- 30% weight on 15min activity
                    CASE WHEN COALESCE(hv.avg_volume_30m_raw, 100000000) > 0
                         THEN rv.volume_30m::float / COALESCE(hv.avg_volume_30m_raw, 100000000)
                         ELSE 0 END * 0.1    -- 10% weight on 30min activity
                ) * (CASE WHEN rv.last_activity IS NOT NULL
                         THEN 1.0 + (1.0 - EXTRACT(EPOCH FROM (:now - rv.last_activity)) / 1800.0)
                         ELSE 1.0 END) DESC  -- Apply recency multiplier
                LIMIT :limit
            """)

            result = self.db.execute(query, {
                'five_min_ago': five_min_ago,
                'fifteen_min_ago': fifteen_min_ago,
                'thirty_min_ago': thirty_min_ago,
                'max_age_ago': max_age_ago,
                'now': now,
                'min_volume_5m': min_volume_5m_lamports,
                'min_volume_15m': min_volume_15m_lamports,
                'min_volume_30m': min_volume_30m_lamports,
                'limit': limit * 2  # Get more results to filter by min anomaly score
            })

            anomalies = []
            for row in result:
                # Calculate overall anomaly score with recency weighting
                overall_score = (
                    row.anomaly_score_5m * 0.6 +   # 60% weight on 5min activity
                    row.anomaly_score_15m * 0.3 +  # 30% weight on 15min activity
                    row.anomaly_score_30m * 0.1    # 10% weight on 30min activity
                ) * row.recency_factor  # Apply recency multiplier

                # Only include if meets minimum anomaly threshold
                if overall_score >= self.MIN_ANOMALY_SCORE:
                    anomalies.append(VolumeAnomaly(
                        mint_address=row.mint_address,
                        token_name=row.token_name,
                        token_symbol=row.token_symbol,
                        volume_1h=float(row.volume_30m) / 1_000_000_000,  # Use 30m as "1h" for compatibility
                        volume_6h=float(row.volume_15m) / 1_000_000_000,  # Use 15m as "6h" for compatibility
                        volume_24h=float(row.volume_5m) / 1_000_000_000,  # Use 5m as "24h" for compatibility
                        avg_volume_1h=float(row.avg_volume_30m) / 1_000_000_000,
                        avg_volume_6h=float(row.avg_volume_15m) / 1_000_000_000,
                        avg_volume_24h=float(row.avg_volume_5m) / 1_000_000_000,
                        anomaly_score_1h=row.anomaly_score_30m,  # Map to existing fields for compatibility
                        anomaly_score_6h=row.anomaly_score_15m,
                        anomaly_score_24h=row.anomaly_score_5m,
                        overall_anomaly_score=overall_score,
                        swap_count_1h=row.swap_count_30m,
                        swap_count_6h=row.swap_count_15m,
                        swap_count_24h=row.swap_count_5m,
                        largest_swap_1h=float(row.largest_swap_5m) / 1_000_000_000,
                        last_activity=row.last_activity
                    ))

            return anomalies[:limit]

        except Exception as e:
            logger.error(f"Error getting volume anomalies: {e}")
            return []

    def get_large_swaps_for_token(self, mint_address: str, limit: int = 5) -> List[LargeSwap]:
        """Get recent large swaps for a specific token using optimized query with short timeframes."""
        try:
            now = datetime.now(timezone.utc)
            max_age_ago = now - timedelta(minutes=self.MAX_TRANSACTION_AGE_MINUTES)

            # Optimized query for large swaps with shorter timeframe
            query = text("""
                SELECT
                    s.signature,
                    s.timestamp,
                    s.mint_address,
                    s.token_amount,
                    s.sol_amount,
                    s.price_per_token,
                    s.swap_type,
                    s.user_wallet,
                    t.name as token_name,
                    t.symbol as token_symbol,
                    t.decimals as token_decimals,
                    EXTRACT(EPOCH FROM (:now - s.timestamp)) as seconds_ago
                FROM swaps s
                JOIN tokens t ON s.mint_address = t.mint_address
                WHERE s.mint_address = :mint_address
                  AND s.sol_amount >= :large_trade_threshold
                  AND s.timestamp >= :max_age_ago
                ORDER BY s.timestamp DESC
                LIMIT :limit
            """)

            result = self.db.execute(query, {
                'mint_address': mint_address,
                'large_trade_threshold': self.LARGE_TRADE_THRESHOLD,
                'max_age_ago': max_age_ago,
                'now': now,
                'limit': limit
            })

            large_swaps = []
            for row in result:
                sol_display = row.sol_amount / 1_000_000_000
                token_display = row.token_amount / (10 ** row.token_decimals)

                large_swaps.append(LargeSwap(
                    signature=row.signature,
                    mint_address=row.mint_address,
                    token_name=row.token_name,
                    token_symbol=row.token_symbol,
                    token_decimals=row.token_decimals,
                    timestamp=row.timestamp,
                    sol_amount=row.sol_amount,
                    token_amount=row.token_amount,
                    sol_display=sol_display,
                    token_display=token_display,
                    price_per_token=row.price_per_token,
                    swap_type=row.swap_type,
                    user_wallet=row.user_wallet,
                    is_whale_trade=row.sol_amount >= self.WHALE_TRADE_THRESHOLD,
                    is_large_trade=row.sol_amount >= self.LARGE_TRADE_THRESHOLD,
                    seconds_ago=row.seconds_ago
                ))

            return large_swaps

        except Exception as e:
            logger.error(f"Error getting large swaps for {mint_address}: {e}")
            return []